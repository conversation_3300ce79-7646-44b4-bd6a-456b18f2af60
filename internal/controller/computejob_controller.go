package controller

import (
	"context"
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	computev1 "chuangcache.com/ai-saas/api/v1"
)

// ComputeJobReconciler 协调 ComputeJob 对象
type ComputeJobReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

//+kubebuilder:rbac:groups=compute.chuangcache.com,resources=computejobs,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=compute.chuangcache.com,resources=computejobs/status,verbs=get;update;patch
//+kubebuilder:rbac:groups=compute.chuangcache.com,resources=computejobs/finalizers,verbs=update
//+kubebuilder:rbac:groups=apps,resources=deployments,verbs=get;list;watch;create;update;patch;delete
//+kubebuilder:rbac:groups=core,resources=pods,verbs=get;list;watch

// Reconcile 处理 ComputeJob 的协调逻辑
func (r *ComputeJobReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := log.FromContext(ctx)

	// 获取 ComputeJob 实例
	var computeJob computev1.ComputeJob
	if err := r.Get(ctx, req.NamespacedName, &computeJob); err != nil {
		if errors.IsNotFound(err) {
			log.Info("ComputeJob resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get ComputeJob")
		return ctrl.Result{}, err
	}

	// 检查是否需要创建 Deployment
	var deployment appsv1.Deployment
	err := r.Get(ctx, types.NamespacedName{Name: computeJob.Name, Namespace: computeJob.Namespace}, &deployment)
	if err != nil && errors.IsNotFound(err) {
		// 创建新的 Deployment
		dep := r.deploymentForComputeJob(&computeJob)
		log.Info("Creating a new Deployment", "Deployment.Namespace", dep.Namespace, "Deployment.Name", dep.Name)
		err = r.Create(ctx, dep)
		if err != nil {
			log.Error(err, "Failed to create new Deployment", "Deployment.Namespace", dep.Namespace, "Deployment.Name", dep.Name)
			return ctrl.Result{}, err
		}
		// 创建成功，更新状态
		computeJob.Status.Phase = "Creating"
		computeJob.Status.Message = "Deployment created successfully"
		err := r.Status().Update(ctx, &computeJob)
		if err != nil {
			log.Error(err, "Failed to update ComputeJob status")
			return ctrl.Result{}, err
		}
		return ctrl.Result{Requeue: true}, nil
	} else if err != nil {
		log.Error(err, "Failed to get Deployment")
		return ctrl.Result{}, err
	}

	// 更新现有 Deployment（如果需要）
	replicas := int32(1)
	if computeJob.Spec.Replicas != nil {
		replicas = *computeJob.Spec.Replicas
	}

	if *deployment.Spec.Replicas != replicas {
		deployment.Spec.Replicas = &replicas
		err = r.Update(ctx, &deployment)
		if err != nil {
			log.Error(err, "Failed to update Deployment", "Deployment.Namespace", deployment.Namespace, "Deployment.Name", deployment.Name)
			return ctrl.Result{}, err
		}
	}

	// 更新状态
	computeJob.Status.ReadyReplicas = deployment.Status.ReadyReplicas
	if deployment.Status.ReadyReplicas == replicas {
		computeJob.Status.Phase = "Running"
		computeJob.Status.Message = "All pods are running"
	} else {
		computeJob.Status.Phase = "Pending"
		computeJob.Status.Message = fmt.Sprintf("Waiting for pods to be ready: %d/%d", deployment.Status.ReadyReplicas, replicas)
	}

	err = r.Status().Update(ctx, &computeJob)
	if err != nil {
		log.Error(err, "Failed to update ComputeJob status")
		return ctrl.Result{}, err
	}

	return ctrl.Result{}, nil
}

// deploymentForComputeJob 为 ComputeJob 创建 Deployment
func (r *ComputeJobReconciler) deploymentForComputeJob(cj *computev1.ComputeJob) *appsv1.Deployment {
	replicas := int32(1)
	if cj.Spec.Replicas != nil {
		replicas = *cj.Spec.Replicas
	}

	labels := map[string]string{
		"app":     "compute-job",
		"compute": cj.Name,
	}

	dep := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      cj.Name,
			Namespace: cj.Namespace,
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: &replicas,
			Selector: &metav1.LabelSelector{
				MatchLabels: labels,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: labels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{{
						Image:   cj.Spec.Image,
						Name:    "compute-container",
						Command: cj.Spec.Command,
						Resources: corev1.ResourceRequirements{
							Requests: corev1.ResourceList{},
						},
					}},
				},
			},
		},
	}

	// 设置资源限制
	if cj.Spec.Resources.CPU != "" {
		dep.Spec.Template.Spec.Containers[0].Resources.Requests[corev1.ResourceCPU] = parseQuantity(cj.Spec.Resources.CPU)
	}
	if cj.Spec.Resources.Memory != "" {
		dep.Spec.Template.Spec.Containers[0].Resources.Requests[corev1.ResourceMemory] = parseQuantity(cj.Spec.Resources.Memory)
	}

	// 设置 Owner Reference 以便级联删除
	ctrl.SetControllerReference(cj, dep, r.Scheme)
	return dep
}

func parseQuantity(s string) resource.Quantity {
	q, _ := resource.ParseQuantity(s)
	return q
}

// SetupWithManager 设置控制器
func (r *ComputeJobReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&computev1.ComputeJob{}).
		Owns(&appsv1.Deployment{}).
		Complete(r)
}
