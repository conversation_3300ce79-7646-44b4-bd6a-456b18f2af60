apiVersion: compute.chuangcache.com/v1
kind: ComputeJob
metadata:
  labels:
    app.kubernetes.io/name: computejob
    app.kubernetes.io/instance: computejob-sample
    app.kubernetes.io/part-of: compute-operator
    app.kubernetes.io/managed-by: kustomize
    app.kubernetes.io/created-by: compute-operator
  name: computejob-jinxq-xxx123
spec:
  replicas: 0
  image: nginx:latest
  command: ["nginx", "-g", "daemon off;"]
  resources:
    cpu: "100m"
    memory: "128Mi"