# This rule is not used by the project container-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the compute.chuangcache.com.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: computejob-editor-role
rules:
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs/status
  verbs:
  - get
