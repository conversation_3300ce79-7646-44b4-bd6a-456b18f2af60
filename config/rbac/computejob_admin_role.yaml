# This rule is not used by the project container-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over compute.chuangcache.com.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: computejob-admin-role
rules:
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs
  verbs:
  - '*'
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs/status
  verbs:
  - get
