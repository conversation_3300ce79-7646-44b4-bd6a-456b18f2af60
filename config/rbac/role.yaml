---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs/finalizers
  verbs:
  - update
- apiGroups:
  - compute.chuangcache.com
  resources:
  - computejobs/status
  verbs:
  - get
  - patch
  - update
