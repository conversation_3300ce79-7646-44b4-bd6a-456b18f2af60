---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: computejobs.compute.chuangcache.com
spec:
  group: compute.chuangcache.com
  names:
    kind: ComputeJob
    listKind: ComputeJobList
    plural: computejobs
    singular: computejob
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.replicas
      name: Replicas
      type: integer
    - jsonPath: .status.readyReplicas
      name: Ready
      type: integer
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: ComputeJob 是 computejobs API 的 Schema
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ComputeJobSpec 定义期望的状态
            properties:
              command:
                description: Command 指定容器启动命令
                items:
                  type: string
                type: array
              image:
                description: Image 指定容器镜像
                type: string
              replicas:
                description: Replicas 指定要创建的 Pod 数量
                format: int32
                type: integer
              resources:
                description: Resources 指定资源限制
                properties:
                  cpu:
                    type: string
                  memory:
                    type: string
                type: object
            required:
            - image
            type: object
          status:
            description: ComputeJobStatus 定义观察到的状态
            properties:
              message:
                description: Message 状态消息
                type: string
              phase:
                description: Phase 表示当前阶段
                type: string
              readyReplicas:
                description: ReadyReplicas 表示就绪的 Pod 数量
                format: int32
                type: integer
            required:
            - readyReplicas
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
