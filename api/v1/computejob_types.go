package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ComputeJobSpec 定义期望的状态
type ComputeJobSpec struct {
	// Replicas 指定要创建的 Pod 数量
	Replicas *int32 `json:"replicas,omitempty"`

	// Image 指定容器镜像
	Image string `json:"image"`

	// Command 指定容器启动命令
	Command []string `json:"command,omitempty"`

	// Resources 指定资源限制
	Resources ResourceRequirements `json:"resources,omitempty"`
}

// ResourceRequirements 定义资源需求
type ResourceRequirements struct {
	CPU    string `json:"cpu,omitempty"`
	Memory string `json:"memory,omitempty"`
}

// ComputeJobStatus 定义观察到的状态
type ComputeJobStatus struct {
	// ReadyReplicas 表示就绪的 Pod 数量
	ReadyReplicas int32 `json:"readyReplicas"`

	// Phase 表示当前阶段
	Phase string `json:"phase,omitempty"`

	// Message 状态消息
	Message string `json:"message,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:printcolumn:name="Replicas",type=integer,JSONPath=`.spec.replicas`
//+kubebuilder:printcolumn:name="Ready",type=integer,JSONPath=`.status.readyReplicas`
//+kubebuilder:printcolumn:name="Phase",type=string,JSONPath=`.status.phase`
//+kubebuilder:printcolumn:name="Age",type=date,JSONPath=`.metadata.creationTimestamp`

// ComputeJob 是 computejobs API 的 Schema
type ComputeJob struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   ComputeJobSpec   `json:"spec,omitempty"`
	Status ComputeJobStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// ComputeJobList 包含 ComputeJob 列表
type ComputeJobList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ComputeJob `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ComputeJob{}, &ComputeJobList{})
}
