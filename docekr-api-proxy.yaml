apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: docker-api-proxy
  labels:
    app: docker-api-proxy
spec:
  selector:
    matchLabels:
      app: docker-api-proxy
  template:
    metadata:
      labels:
        app: docker-api-proxy
    spec:
      containers:
      - name: socat
        image: docker.1ms.run/alpine/socat:*******
        securityContext:
          privileged: true  # Required to access /var/run/docker.sock
        volumeMounts:
        - name: docker-sock
          mountPath: /var/run/docker.sock
        command: ["sh", "-c"]
        args:
          - |
            socat tcp-listen:2375,fork,reuseaddr unix-connect:/var/run/docker.sock
      restartPolicy: Always
      volumes:
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
---
apiVersion: v1
kind: Service
metadata:
  name: docker-api-proxy
  labels:
    app: docker-api-proxy
spec:
  selector:
    app: docker-api-proxy
  ports:
  - name: docker-api
    port: 2375
    targetPort: 2375